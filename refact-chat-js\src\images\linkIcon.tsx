import React from "react";
import classnames from "classnames";
import styles from "./linkIcon.module.css";

export const LinkIcon: React.FC<
  React.SVGProps<SVGSVGElement> & { className?: string }
> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0.03587532043457031 277.27899169921875 277.2080993652344"
      width="13.5"
      height="13.5"
      {...props}
      className={classnames(styles.linkIcon, className)}
    >
      <g>
        <path
          d="m149.245 191.671-42.425 42.426-.001.001-.001.001c-17.544 17.545-46.092 17.546-63.638 0-8.5-8.5-13.18-19.801-13.18-31.82 0-12.018 4.68-23.317 13.177-31.817l.003-.003 42.425-42.426c5.857-5.858 5.857-15.356-.001-21.213-5.857-5.857-15.355-5.857-21.213 0l-42.425 42.426-.009.01C7.798 163.42 0 182.251 0 202.279c0 20.033 7.801 38.867 21.967 53.033C36.589 269.933 55.794 277.244 75 277.244c19.206 0 38.412-7.311 53.032-21.932v-.001l.001-.001 42.425-42.426c5.857-5.857 5.857-15.355-.001-21.213-5.856-5.857-15.353-5.857-21.212 0zM277.279 75c0-20.033-7.802-38.867-21.968-53.033-29.243-29.242-76.824-29.241-106.065 0l-.004.005-42.424 42.423c-5.858 5.857-5.858 15.356 0 21.213a14.952 14.952 0 0 0 10.607 4.394c3.838 0 7.678-1.465 10.606-4.394l42.424-42.423.005-.005c17.544-17.544 46.092-17.545 63.638 0 8.499 8.5 13.181 19.801 13.181 31.82 0 12.018-4.68 23.317-13.178 31.817l-.003.003-42.425 42.426c-5.857 5.857-5.857 15.355.001 21.213a14.954 14.954 0 0 0 10.606 4.394c3.839 0 7.678-1.465 10.607-4.394l42.425-42.426.009-.01C269.48 113.859 277.279 95.028 277.279 75z"
          fill="currentColor"
          opacity="1"
          data-original="currentColor"
        ></path>
        <path
          d="M85.607 191.671a14.954 14.954 0 0 0 10.606 4.394c3.839 0 7.678-1.465 10.607-4.394l84.852-84.852c5.858-5.857 5.858-15.355 0-21.213-5.857-5.857-15.355-5.857-21.213 0l-84.852 84.851c-5.858 5.859-5.858 15.357 0 21.214z"
          fill="currentColor"
          opacity="1"
          data-original="currentColor"
        ></path>
      </g>
    </svg>
  );
};
