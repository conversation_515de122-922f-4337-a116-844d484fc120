{
	"compilerOptions": {
		"module": "commonjs",
		"target": "ES2020",
		"outDir": "out",
		"lib": [
			"ES2020",
			"ES2021.WeakRef",
			"DOM",
		],
		"sourceMap": true,
		"rootDir": "src",
		"strict": true,
		"esModuleInterop": true,
		/* enable all strict type-checking options */
		/* Additional Checks */
		// "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
		// "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
		// "noUnusedParameters": true,  /* Report errors on unused parameters. */
	}
}
