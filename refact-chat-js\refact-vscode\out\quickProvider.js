"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateQuickActions = exports.QuickActionProvider = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const events_1 = require("refact-chat-js/dist/events");
class QuickActionProvider {
    provideCodeActions(document, range, context, token) {
        // Create new instances of Refact.ai actions
        const refactActions = QuickActionProvider.actions.map(action => {
            const newAction = new vscode.CodeAction(action.title, action.kind);
            if (action.command) {
                const diagnosticRange = context.diagnostics[0]?.range || range;
                newAction.command = {
                    ...action.command,
                    arguments: [
                        action.command.arguments?.[0],
                        action.command.arguments?.[1],
                        {
                            range: diagnosticRange,
                            diagnostics: context.diagnostics
                        }
                    ]
                };
            }
            return newAction;
        });
        return refactActions;
    }
    static sendQuickActionToChat(messageBlock) {
        if (!global || !global.side_panel || !global.side_panel._view) {
            return;
        }
        const message = (0, events_1.setInputValue)({
            value: messageBlock,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            send_immediately: true
        });
        global.side_panel._view.webview.postMessage(message);
    }
    static async handleAction(actionId, command, context) {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor');
            return;
        }
        const filePath = vscode.window.activeTextEditor?.document.fileName || "";
        const workspaceFolders = vscode.workspace.workspaceFolders;
        let relativePath = "";
        if (workspaceFolders) {
            const workspacePath = workspaceFolders[0].uri.fsPath;
            relativePath = path.relative(workspacePath, filePath);
        }
        const selection = editor.selection;
        let codeSnippet = '';
        let middleLineOfSelection = 0;
        let diagnosticMessage = '';
        // if no diagnostic were present, taking user's selection instead
        if (actionId === 'bugs' && context?.diagnostics && context.diagnostics.length > 0) {
            const diagnostic = context.diagnostics[0];
            diagnosticMessage = diagnostic.message;
            middleLineOfSelection = diagnostic.range.start.line;
            codeSnippet = editor.document.getText(diagnostic.range);
        }
        else {
            codeSnippet = editor.document.getText(selection);
            middleLineOfSelection = Math.floor((selection.start.line + selection.end.line) / 2);
        }
        if (!codeSnippet) {
            const cursorPosition = selection.isEmpty ? editor.selection.active : selection.start;
            const startLine = Math.max(0, cursorPosition.line - 2);
            const endLine = Math.min(editor.document.lineCount - 1, cursorPosition.line + 2);
            const range = new vscode.Range(startLine, 0, endLine, editor.document.lineAt(endLine).text.length);
            codeSnippet = editor.document.getText(range);
            middleLineOfSelection = cursorPosition.line;
        }
        const messageBlock = command.messages.map(({ content }) => (content
            // we should fetch default prompt somehow
            .replace("%PROMPT_DEFAULT%", '')
            .replace("%CURRENT_FILE_PATH_COLON_CURSOR%", '')
            .replace("%CURRENT_FILE%", filePath)
            .replace("%CURSOR_LINE%", (middleLineOfSelection + 1).toString())
            .replace("%CODE_SELECTION%", codeSnippet + "\n"))).join("\n");
        vscode.commands.executeCommand("refactaicmd.callChat");
        this.sendQuickActionToChat(messageBlock);
    }
}
exports.QuickActionProvider = QuickActionProvider;
_a = QuickActionProvider;
QuickActionProvider.actions = [];
QuickActionProvider.quickActionDisposables = [];
QuickActionProvider.providedCodeActionKinds = [
    vscode.CodeActionKind.QuickFix,
    vscode.CodeActionKind.RefactorRewrite
];
QuickActionProvider.updateActions = async (toolboxCommands) => {
    _a.actions = Object.entries(toolboxCommands).map(([id, command]) => {
        if (id === 'help') {
            return;
        }
        let action;
        if (id === 'bugs') {
            action = new vscode.CodeAction('Refact.ai: ' + command.description, vscode.CodeActionKind.QuickFix);
        }
        else {
            action = new vscode.CodeAction('Refact.ai: ' + command.description, vscode.CodeActionKind.RefactorRewrite);
        }
        action.command = {
            command: 'refactcmd.' + id,
            title: 'Refact.ai: ' + command.description,
            arguments: [id, command]
        };
        return action;
    }).filter((action) => action !== undefined);
    const dispose = (disposables) => {
        disposables.forEach(d => d.dispose());
    };
    dispose(_a.quickActionDisposables);
    _a.actions.forEach(action => {
        if (action.command) {
            try {
                // XXX: this returns disposable, we need to dispose of old before setting new
                let disposable = vscode.commands.registerCommand(action.command.command, (actionId, command, context) => {
                    QuickActionProvider.handleAction(actionId, command, context);
                });
                _a.quickActionDisposables.push(disposable);
            }
            catch (e) {
                console.error('Error registering command', e);
            }
        }
    });
};
exports.updateQuickActions = QuickActionProvider.updateActions;
//# sourceMappingURL=quickProvider.js.map