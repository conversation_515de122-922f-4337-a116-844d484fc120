{"version": 3, "file": "codeLens.js", "sourceRoot": "", "sources": ["../src/codeLens.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yDAAyD;AACzD,+CAAiC;AACjC,2CAA6B;AAC7B,iDAAmC;AACnC,kDAAoC;AACpC,qDAAuC;AACvC,uDAOoC;AAGpC,MAAM,gBAAiB,SAAQ,MAAM,CAAC,QAAQ;IAC1C,YACI,KAAmB,EACnB,GAAW,EACX,IAAY,EACZ,IAAY;QAEZ,KAAK,CAAC,KAAK,EAAE;YACT,KAAK,EAAE,GAAG;YACV,OAAO,EAAE,6BAA6B;YACtC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;SACjC,CAAC,CAAC;IACP,CAAC;CACJ;AAEU,QAAA,gBAAgB,GAAkC,IAAI,CAAC;AAClE,MAAa,YAAY;IAKrB;QAEI,IAAI,CAAC,uBAAuB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;QAC/D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,iBAAiB,CACnB,QAA6B;QAG7B,MAAM,iBAAiB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,GAAG,CAAU,UAAU,CAAC,IAAI,IAAI,CAAC;QACzG,IAAI,CAAC,iBAAiB,EAAE;YACpB,OAAO,EAAE,CAAC;SACb;QACD,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,GAAG,CAAU,eAAe,CAAC,IAAI,KAAK,CAAC;QACnG,IAAI,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,EAAE,CAAC;SACb;QAED,IAAI,aAAa,GAAG,MAAM,QAAQ,CAAC,wBAAwB,EAAE,CAAC;QAE9D,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE;YACrC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACL,cAAc,EAAE,kBAAkB;aACrC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACjB,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC5B,KAAK,EAAE,KAAK;aACf,CAAC;SACL,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,MAAM,GAAsB,EAAE,CAAC;QACnC,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;YACzB,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,cAAc,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;SAExD;aAAM,IAAI,WAAW,IAAI,aAAa,EAAE;YACrC,wBAAgB,GAAG,aAAa,CAAC,WAAW,CAA2B,CAAC;YACxE,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC7C,IAAI,QAAQ,IAAI,cAAc,EAAE;gBAC5B,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAClE;YACD,IAAI,WAAW,IAAI,cAAc,EAAE;gBAC/B,KAAK,IAAI,CAAC,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC9D,IAAI,IAAI,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,IAAI,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;oBACzE,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE;wBACtB,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,wBAAgB,CAAC,EAAE;4BAC5D,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE,cAAc,GAAG,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;yBACnG;qBACJ;yBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,EAAE;wBACpC,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;qBAC/E;yBAAM;wBACH,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,0BAA0B,CAAC,CAAC,CAAC;qBACpE;iBACJ;aACJ;SACJ;QAED,IAAI,KAAK,CAAC,aAAa,GAAG,QAAQ,CAAC,SAAS,EAAE;YAC1C,IAAI,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;YAC7E,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,kBAAkB,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;YAC5E,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;YAC1E,6GAA6G;SAChH;QAED,KAAK,CAAC,mCAAmC,GAAG,KAAK,CAAC;QAClD,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AA7ED,oCA6EC;AAED,MAAM,kBAAkB,GAAG,CACvB,QAAuB,EACvB,aAAqB,EACrB,IAAY,EACZ,cAAuB,KAAK,EAC9B,EAAE;IACA,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE;QACzC,OAAO;KACV;IAED,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjC,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,IAAA,sBAAa,EAAC,YAAY,CAAC,CAAC;IAClF,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC;IAE7E,IAAI,CAAC,oBAAoB,EAAE;QACvB,MAAM,iBAAiB,GAAG,iCAAiC,CAAC,QAAQ,EAAE,aAAa,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QACnG,oBAAoB,CAAC;YACjB,QAAQ,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,WAAW;SAC7D,CAAC,CAAC;QACH,OAAO;KACV;IAED,MAAM,YAAY,GAAG,kBAAkB,CAAC,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACnF,oBAAoB,CAAC;QACjB,KAAK,EAAE,YAAY,EAAE,gBAAgB,EAAE,WAAW;KACrD,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAAC,EAAC,QAAQ,EAAE,KAAK,EAAE,gBAAgB,EAAwE,EAAE,EAAE;IACxI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE;QAC3B,OAAO;KACV;IAAA,CAAC;IAEF,MAAM,YAAY,GAAG,IAAA,sBAAa,EAAC;QAC/B,QAAQ;QACR,KAAK;QACL,gBAAgB;KACnB,CAAC,CAAC;IACH,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AAC9D,CAAC,CAAC;AAEF,MAAM,sBAAsB,GAAG,CAC3B,IAAY,EACZ,aAAqB,EACrB,MAAqB,EACrB,cAAsB,EACxB,EAAE;IACA,OAAO,IAAI;SACN,OAAO,CAAC,gBAAgB,EAAE,aAAa,CAAC;SACxC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC/D,OAAO,CAAC,kBAAkB,EAAE,cAAc,CAAC;SAC3C,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;AACnD,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,CACvB,OAAoB,EACpB,aAAqB,EACrB,MAAqB,EACrB,IAAY,EACd,EAAE;IACA,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;QACrC,OAAO,sBAAsB,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KAC/E;SAAM;QACH,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACjC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;gBAChD,OAAO,sBAAsB,CAAC,OAAO,CAAC,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;aAC5E;QACL,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACjB;AACL,CAAC,CAAC;AAEF,MAAM,iCAAiC,GAAG,CACtC,QAAuB,EACvB,aAAqB,EACrB,MAAqB,EACrB,IAAY,EACd,EAAE;IACA,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;QAC1B,IAAI,IAAA,sBAAa,EAAC,OAAO,CAAC,EAAE;YACxB,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;gBACrC,OAAO;oBACH,GAAG,OAAO;oBACV,OAAO,EAAE,sBAAsB,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,IAAI,CAAC;iBAChF,CAAC;aACL;SACJ;QACD,OAAO,OAAO,CAAC;IACnB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEK,KAAK,UAAU,iBAAiB,CAAC,SAAiB,EAAE,KAAU;IACjE,IAAI,CAAC,MAAM,EAAE;QAAE,OAAO;KAAE;IACxB,IAAI,MAAM,CAAC,iBAAiB,EAAE;QAAE,OAAO;KAAE;IACzC,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAChC,IAAI,wBAAgB,EAAE;QAClB,MAAM,WAAW,GAAG,wBAAgB,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC;QAC/D,MAAM,OAAO,GAAG,wBAAgB,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC;QACvD,IAAI,QAAQ,GAAkB,wBAAgB,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC;QAEtE,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/D,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/D,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAEjE,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC;QAC1E,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,aAAa,GAAW,EAAE,CAAC;QAE/B,IAAI,gBAAgB,EAAE;YAClB,MAAM,aAAa,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;YACrD,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;SAC3D;QAED,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAEzE,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,MAAM,CAAC,iBAAiB,GAAG,KAAK,CAAC;YACjC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAC9C,IAAI,CAAC,MAAM,EAAE;gBACT,OAAO;aACV;YACD,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YACpE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAChC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;YAC3D,OAAO;SACV;QAED,IAAI,MAAM,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE;YAC3F,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAC5E,IAAI,OAAO,YAAY,KAAK,QAAQ,IAAI,YAAY,KAAK,QAAQ,IAAI,OAAO,EAAE;gBAC1E,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;aAC9D;YACD,kBAAkB,CAAC,QAAQ,EAAE,aAAa,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;YAC/D,IAAI,CAAC,WAAW,EAAE;gBACd,MAAM,CAAC,iBAAiB,GAAG,KAAK,CAAC;aACpC;SACJ;aAAM;YACH,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;YAC3D,kBAAkB,CAAC,QAAQ,EAAE,aAAa,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;YAC/D,IAAI,CAAC,WAAW,EAAE;gBACd,MAAM,CAAC,iBAAiB,GAAG,KAAK,CAAC;aACpC;SACJ;KACJ;AACL,CAAC;AArDD,8CAqDC;AAGU,QAAA,eAAe,GAAwB,IAAI,CAAC;AAGvD,SAAgB,aAAa,CAAC,QAAsB;IAEhD,uBAAe,GAAG,QAAQ,CAAC;AAC/B,CAAC;AAHD,sCAGC;AAGD,SAAgB,aAAa;IAEzB,IAAI,uBAAe,EAAE;QACjB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,uBAAe,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC;KAClD;AACL,CAAC;AAND,sCAMC"}