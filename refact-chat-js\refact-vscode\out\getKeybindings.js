"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getKeyBindingForChat = exports.getKeybindings = void 0;
const vscode = __importStar(require("vscode"));
const path_1 = __importDefault(require("path"));
const json5_1 = __importDefault(require("json5"));
const isInsiders = vscode.version.includes("insider");
const codeFolder = isInsiders ? "Code - Insiders" : "Code";
const configPaths = {
    windows: path_1.default.join(process.env.APPDATA || "", codeFolder),
    macos: path_1.default.join(process.env.HOME || "", "Library", "Application Support", codeFolder),
    linux: path_1.default.join(process.env.HOME || "", "config", codeFolder)
};
function getSystem() {
    switch (process.platform) {
        case "aix": return "linux";
        case "darwin": return "macos";
        case "freebsd": return "linux";
        case "linux": return "linux";
        case "openbsd": return "linux";
        case "sunos": return "linux";
        case "win32": return "windows";
        default: return "windows";
    }
}
function getPathToConfigFile() {
    // XXX: use any other way to read keybindings.json, so it doesn't get to the LSP
    const system = getSystem();
    const directoryForSystem = configPaths[system];
    const configDir = process.env.VSCODE_PORTABLE ? path_1.default.join(process.env.VSCODE_PORTABLE, "user-data", "User") : directoryForSystem;
    const pathToFile = path_1.default.join(configDir, "User", "keybindings.json");
    return pathToFile;
}
async function getUserConfig(path) {
    try {
        const doc = await vscode.workspace.openTextDocument(path);
        const text = doc.getText();
        const json = json5_1.default.parse(text);
        return json;
    }
    catch (e) {
        return [];
    }
}
async function getKeybindings(key) {
    const pathToConfigFile = getPathToConfigFile();
    const defaultKeyBindings = require("../package.json").contributes.keybindings;
    const userConfig = await getUserConfig(pathToConfigFile);
    const allKeyBindings = [...defaultKeyBindings, ...userConfig];
    const data = allKeyBindings.reduce((a, b) => {
        a[b.command] = b.key;
        return a;
    }, {});
    if (key) {
        return data[key];
    }
    else {
        return data;
    }
}
exports.getKeybindings = getKeybindings;
async function getKeyBindingForChat(name) {
    const system = getSystem();
    let key = await getKeybindings(name);
    if (system === "macos") {
        key = key
            .replace("alt", "⌥")
            .replace("ctrl", "⌃")
            .replace("cmd", "⌘")
            .toLocaleUpperCase();
        return key;
    }
    return key.replace(/\w+/g, w => (w.substring(0, 1).toUpperCase()) + w.substring(1));
}
exports.getKeyBindingForChat = getKeyBindingForChat;
//# sourceMappingURL=getKeybindings.js.map