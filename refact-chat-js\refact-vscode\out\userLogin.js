"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.secret_api_key = exports.account_message = exports.welcome_message = exports.login_message = exports.get_address = void 0;
/* eslint-disable @typescript-eslint/naming-convention */
const vscode = __importStar(require("vscode"));
function get_address() {
    let addr1 = vscode.workspace.getConfiguration().get("refactai.addressURL");
    let addr2 = vscode.workspace.getConfiguration().get("refactai.infurl"); // old name
    return addr1 || addr2 || "";
}
exports.get_address = get_address;
async function login_message() {
    await vscode.commands.executeCommand('workbench.view.extension.refact-toolbox-pane');
}
exports.login_message = login_message;
async function welcome_message() {
    await vscode.commands.executeCommand('workbench.view.extension.refact-toolbox-pane');
    await new Promise(resolve => setTimeout(resolve, 1000));
    await vscode.commands.executeCommand('workbench.view.extension.refact-toolbox-pane');
    let selection = await vscode.window.showInformationMessage("Welcome to Refact.ai!\nConnect to AI inference server in sidebar.");
}
exports.welcome_message = welcome_message;
async function account_message(info, action, url) {
    let selection = await vscode.window.showInformationMessage(info, action);
    if (selection === action) {
        vscode.env.openExternal(vscode.Uri.parse(url));
    }
}
exports.account_message = account_message;
function secret_api_key() {
    let key = vscode.workspace.getConfiguration().get('refactai.apiKey');
    if (!key) {
        // Backward compatibility: codify is the old name
        key = vscode.workspace.getConfiguration().get('codify.apiKey');
    }
    if (!key) {
        return "";
    }
    if (typeof key !== 'string') {
        return "";
    }
    return key;
}
exports.secret_api_key = secret_api_key;
//# sourceMappingURL=userLogin.js.map