"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.show_message_from_server = void 0;
/* eslint-disable @typescript-eslint/naming-convention */
const events_1 = require("refact-chat-js/dist/events");
const vscode = __importStar(require("vscode"));
const HTML_TAG_A_REGULAR_EXPRESSION = /<a\s+href="([^"]+)">([^<]+)<\/a>/i;
async function show_message_from_server(kind_of_message, msg) {
    // Show a message from the server, but only once.
    let context_ = global.global_context;
    if (context_ === undefined) {
        return false;
    }
    let context = context_;
    let already_seen = context.globalState.get(`refactai.servermsg${kind_of_message}`);
    if (already_seen === undefined) {
        already_seen = "";
    }
    if (already_seen === msg) {
        return false;
    }
    if (msg === "") {
        return false;
    }
    const message_match_link = msg.match(HTML_TAG_A_REGULAR_EXPRESSION);
    let message_text = msg;
    let link_label;
    let link_href;
    if (message_match_link) {
        link_href = message_match_link[1];
        link_label = message_match_link[2];
        message_text = msg.replace(HTML_TAG_A_REGULAR_EXPRESSION, link_label);
    }
    if (link_href && link_label) {
        const button_label = (0, events_1.toPascalCase)(link_label);
        vscode.window.showInformationMessage(message_text, button_label).then((selection) => {
            if (selection === button_label && link_href) {
                try {
                    const uri = vscode.Uri.parse(link_href, true);
                    vscode.env.openExternal(uri)
                        .then(success => {
                        if (!success) {
                            vscode.window.showErrorMessage("Failed to open URL");
                        }
                    }, error => {
                        vscode.window.showErrorMessage(`Failed to open URL: ${error}`);
                    });
                }
                catch (error) {
                    console.error(error);
                    vscode.window.showErrorMessage(`Failed to open URL: ${error}`);
                }
            }
        });
    }
    else {
        vscode.window.showInformationMessage(msg);
    }
    await context.globalState.update(`refactai.servermsg${kind_of_message}`, msg);
}
exports.show_message_from_server = show_message_from_server;
//# sourceMappingURL=usabilityHints.js.map