{"version": 3, "file": "storeVersions.js", "sourceRoot": "", "sources": ["../src/storeVersions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yDAAyD;AACzD,+CAAiC;AAGjC,SAAgB,sBAAsB,CAAC,QAA6B;IAEhE,IAAI,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;IACxC,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC;IACpF,IAAI,WAAW,KAAK,SAAS,IAAI,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;QAChE,yEAAyE;QACzE,IAAI,kBAAkB,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACjE,IAAI,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACpC,kBAAkB,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACxD;QACD,OAAO,kBAAkB,CAAC;KAC7B;IACD,iEAAiE;IACjE,IAAI,UAAU,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC5C,IAAI,UAAU,IAAI,CAAC,EAAE;QACjB,OAAO,SAAS,CAAC,SAAS,CAAC,UAAU,GAAC,CAAC,CAAC,CAAC;KAC5C;IACD,OAAO,SAAS,CAAC;AACrB,CAAC;AAlBD,wDAkBC"}