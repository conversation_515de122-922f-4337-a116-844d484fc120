name: Publish

on:
  workflow_dispatch:
    inputs:
      lsp_branch:
        description: "Branch name of lsp"
        default: "main"
      chat_js_branch:
        description: "Branch name of chat-js"
        default: "main"
      stable_release:
        description: "Publish stable release version"
        default: false
        type: boolean

jobs:
  publish:
    strategy:
      matrix:
        include:
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            code-target: win32-x64
          - os: windows-latest
            target: aarch64-pc-windows-msvc
            code-target: win32-arm64
          - os: ubuntu-22.04
            target: x86_64-unknown-linux-gnu
            code-target: linux-x64
          - os: ubuntu-22.04
            target: aarch64-unknown-linux-gnu
            code-target: linux-arm64
          # - os: ubuntu-20.04
          #   target: armv7-unknown-linux-gnueabihf
          #   code-target: linux-armhf
          - os: macos-13
            target: x86_64-apple-darwin
            code-target: darwin-x64
          - os: macos-14
            target: aarch64-apple-darwin
            code-target: darwin-arm64

    env:
      LLM_LS_TARGET: ${{ matrix.target }}

    name: Publish in marketplace (VSCE) (${{ matrix.target }})
    runs-on: ${{ matrix.os }}
    container: ${{ matrix.container }}

    steps:
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: ${{ env.FETCH_DEPTH }}

      - name: Download lsp artifacts
        id: download-artifact-lsp
        uses: dawidd6/action-download-artifact@v3
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          workflow: agent_engine_build.yml
          repo: smallcloudai/refact
          branch: ${{ inputs.lsp_branch }}
          path: ./assets
          name: dist-${{ matrix.target }}
      
      - name: Download chat artifacts
        id: download-artifact-chat
        uses: dawidd6/action-download-artifact@v9
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          workflow: agent_gui_build.yml
          repo: smallcloudai/refact
          branch: ${{ inputs.chat_js_branch }}
          path: ./chat_package
          name: lts-refact-chat-js-.*\.tgz
          name_is_regexp: true

      - name: Prepare chat package
        shell: bash
        run: |
          mkdir -p ./chat_package_fixed
          find ./chat_package -name "*.tgz" -type f -exec cp {} ./chat_package_fixed/ \;
          ls -la ./chat_package_fixed

      - name: Install VSCE
        shell: bash
        run: |
          npm install ./chat_package_fixed/*.tgz
          npm install -g @vscode/vsce
          rm -rf ./chat_package ./chat_package_fixed

      - name: Package VSCE extension
        shell: bash
        run: |
          chmod +x ./assets/refact-lsp*
          if [[ ${{ inputs.stable_release }}  != "true" ]]; then
            export PRERELEASE=--pre-release
          fi
          echo "PRERELEASE=${PRERELEASE}"
          vsce package --target ${{ matrix.code-target }} ${PRERELEASE}

      - name: Getting version
        id: get_version  # Add this ID to reference the output
        shell: bash
        run: |
          VERSION=$(cat package.json | grep \"version\": | head -n1 | cut -d'"' -f4)
          echo "version=$VERSION" >> $GITHUB_OUTPUT  # Store version in GitHub output

      - name: Release in GH
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ github.token }}
          file: ./*.vsix
          prerelease: ${{ !inputs.stable_release }}
          tag: v${{ steps.get_version.outputs.version }}  # Use the version from previous step with 'v' prefix
          overwrite: true
          target_commit: ${{ github.sha }}
          file_glob: true

      - name: Publish VSCE extension
        shell: bash
        run: |
          if [[ ${{ inputs.stable_release }}  != "true" ]]; then
            export PRERELEASE=--pre-release
          fi
          echo "PRERELEASE=${PRERELEASE}"
          vsce publish --skip-duplicate --pat ${{secrets.VSCE_PAT}} ${PRERELEASE} --packagePath *.vsix

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: vscode-plugin-${{ matrix.target }}
          path: ./*.vsix
  notify:
    needs: publish
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: ${{ env.FETCH_DEPTH }}

      - name: Setup vars
        shell: bash
        id: setupvars
        run: |
          if [[ ${{ inputs.stable_release }}  != "true" ]]; then
            echo "slack_notification_channel=prerelease" >> "$GITHUB_OUTPUT"
          else
            echo "slack_notification_channel=stable" >> "$GITHUB_OUTPUT"
          fi
          echo "plugin_version=$(cat package.json | jq -r '.version')" >> "$GITHUB_OUTPUT"

      - name: Post to a Slack channel
        id: slack
        uses: slackapi/slack-github-action@v1.26.0
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "VSCode plugin ${{ steps.setupvars.outputs.plugin_version }} is released in ${{ steps.setupvars.outputs.slack_notification_channel }} channel",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "by ${{ github.actor }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Notify to Discord
        run: |
          curl -X POST ${{ secrets.DISCORD_WEBHOOK_URL }} \
          -H "Content-Type: application/json" \
          -d '{"msg":"VSCode plugin ${{ steps.setupvars.outputs.plugin_version }} is released in ${{ steps.setupvars.outputs.slack_notification_channel }} channel"}'
