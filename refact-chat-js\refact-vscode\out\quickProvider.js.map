{"version": 3, "file": "quickProvider.js", "sourceRoot": "", "sources": ["../src/quickProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAE7B,uDAEoC;AASpC,MAAa,mBAAmB;IAS5B,kBAAkB,CACd,QAA6B,EAC7B,KAAsC,EACtC,OAAiC,EACjC,KAA+B;QAE/B,4CAA4C;QAC5C,MAAM,aAAa,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC3D,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YACnE,IAAI,MAAM,CAAC,OAAO,EAAE;gBAChB,MAAM,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,KAAK,CAAC;gBA<PERSON>/D,SAAS,CAAC,OAAO,GAAG;oBAChB,GAAG,MAAM,CAAC,OAAO;oBACjB,SAAS,EAAE;wBACP,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;wBAC7B,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;wBAC7B;4BACI,KAAK,EAAE,eAAe;4BACtB,WAAW,EAAE,OAAO,CAAC,WAAW;yBACnC;qBACJ;iBACJ,CAAC;aACL;YACD,OAAO,SAAS,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;IACzB,CAAC;IA2CM,MAAM,CAAC,qBAAqB,CAAC,YAAoB;QACpD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE;YAC3D,OAAO;SACV;QACD,MAAM,OAAO,GAAG,IAAA,sBAAa,EAAC;YAC1B,KAAK,EAAE,YAAY;YACnB,gEAAgE;YAChE,gBAAgB,EAAE,IAAI;SACzB,CAAC,CAAC;QACH,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACzD,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAuB,EAAE,OAAmE;QAC3I,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;YACnD,OAAO;SACV;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC;QACzE,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,YAAY,GAAW,EAAE,CAAC;QAE9B,IAAI,gBAAgB,EAAE;YAClB,MAAM,aAAa,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;YACrD,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;SACzD;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,qBAAqB,GAAG,CAAC,CAAC;QAC9B,IAAI,iBAAiB,GAAG,EAAE,CAAC;QAE3B,iEAAiE;QACjE,IAAI,QAAQ,KAAK,MAAM,IAAI,OAAO,EAAE,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/E,MAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAC1C,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC;YACvC,qBAAqB,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;YAEpD,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SAC3D;aAAM;YACH,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjD,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SACvF;QAED,IAAI,CAAC,WAAW,EAAE;YACd,MAAM,cAAc,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC;YACrF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YACvD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,cAAc,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YACjF,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnG,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC7C,qBAAqB,GAAG,cAAc,CAAC,IAAI,CAAC;SAC/C;QAED,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAC,OAAO,EAAC,EAAE,EAAE,CAAC,CACrD,OAAO;YACH,yCAAyC;aACxC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;aAC/B,OAAO,CAAC,kCAAkC,EAAE,EAAE,CAAC;aAC/C,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC;aACnC,OAAO,CAAC,eAAe,EAAE,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;aAChE,OAAO,CAAC,kBAAkB,EAAE,WAAW,GAAG,IAAI,CAAC,CACvD,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;QACvD,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;IAC7C,CAAC;;AAlJL,kDAmJC;;AAlJkB,2BAAO,GAAwB,EAAE,CAAC;AAClC,0CAAsB,GAAwB,EAAE,CAAC;AAEzC,2CAAuB,GAAG;IAC7C,MAAM,CAAC,cAAc,CAAC,QAAQ;IAC9B,MAAM,CAAC,cAAc,CAAC,eAAe;CACxC,CAAC;AAgCY,iCAAa,GAAG,KAAK,EAAE,eAA+C,EAAE,EAAE;IACpF,EAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;QACjE,IAAG,EAAE,KAAK,MAAM,EAAE;YAAE,OAAO;SAAE;QAC7B,IAAI,MAAM,CAAC;QACX,IAAG,EAAE,KAAK,MAAM,EAAE;YACd,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,aAAa,GAAG,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;SACvG;aAAM;YACH,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,aAAa,GAAG,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;SAC9G;QACD,MAAM,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,YAAY,GAAG,EAAE;YAC1B,KAAK,EAAE,aAAa,GAAG,OAAO,CAAC,WAAW;YAC1C,SAAS,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC;SAC3B,CAAC;QACF,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,EAA+B,EAAE,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;IAEzE,MAAM,OAAO,GAAG,CAAC,WAAgC,EAAE,EAAE;QACjD,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,OAAO,CAAC,EAAI,CAAC,sBAAsB,CAAC,CAAC;IAErC,EAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC1B,IAAI,MAAM,CAAC,OAAO,EAAE;YAChB,IAAI;gBACA,6EAA6E;gBAC7E,IAAI,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAC5C,MAAM,CAAC,OAAO,CAAC,OAAO,EACtB,CAAC,QAAgB,EAAE,OAAuB,EAAE,OAAmE,EAAE,EAAE;oBAC/G,mBAAmB,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBACjE,CAAC,CACJ,CAAC;gBACF,EAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChD;YAAC,OAAO,CAAC,EAAE;gBACR,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;aACjD;SACJ;IACL,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAuEO,QAAA,kBAAkB,GAAG,mBAAmB,CAAC,aAAa,CAAC"}