{"version": 3, "file": "getKeybindings.js", "sourceRoot": "", "sources": ["../src/getKeybindings.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,gDAAwB;AACxB,kDAA0B;AAE1B,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAEtD,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC;AAC3D,MAAM,WAAW,GAAG;IACnB,OAAO,EAAE,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,EAAE,UAAU,CAAC;IACzD,KAAK,EAAE,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,SAAS,EAAE,qBAAqB,EAAE,UAAU,CAAC;IACtF,KAAK,EAAE,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,QAAQ,EAAE,UAAU,CAAC;CAC9D,CAAC;AAGF,SAAS,SAAS;IACd,QAAQ,OAAO,CAAC,QAAQ,EAAE;QAC5B,KAAK,KAAK,CAAC,CAAC,OAAO,OAAO,CAAC;QAC3B,KAAK,QAAQ,CAAC,CAAC,OAAO,OAAO,CAAC;QACxB,KAAK,SAAS,CAAC,CAAC,OAAO,OAAO,CAAC;QAC/B,KAAK,OAAO,CAAC,CAAC,OAAO,OAAO,CAAC;QAC7B,KAAK,SAAS,CAAC,CAAC,OAAO,OAAO,CAAC;QACrC,KAAK,OAAO,CAAC,CAAC,OAAO,OAAO,CAAC;QAC7B,KAAK,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;QACzB,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;KAChC;AACF,CAAC;AAED,SAAS,mBAAmB;IACxB,gFAAgF;IAChF,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAC3B,MAAM,kBAAkB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAC/C,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,WAAW,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC;IAEhI,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,kBAAkB,CAAC,CAAC;IACpE,OAAO,UAAU,CAAC;AAEtB,CAAC;AAOD,KAAK,UAAU,aAAa,CAAC,IAAY;IACrC,IAAI;QACA,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC1D,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;QAC3B,MAAM,IAAI,GAAiB,eAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;KACf;IAAC,OAAO,CAAC,EAAE;QACR,OAAO,EAAE,CAAC;KACb;AACL,CAAC;AAMM,KAAK,UAAU,cAAc,CAAC,GAAY;IAC7C,MAAM,gBAAgB,GAAG,mBAAmB,EAAE,CAAC;IAC/C,MAAM,kBAAkB,GAAiB,OAAO,CAAC,iBAAiB,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC;IAC5F,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAGzD,MAAM,cAAc,GAAG,CAAC,GAAG,kBAAkB,EAAE,GAAG,UAAU,CAAC,CAAC;IAC9D,MAAM,IAAI,GAAG,cAAc,CAAC,MAAM,CAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAChE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QACrB,OAAO,CAAC,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAG,GAAG,EAAC;QACH,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;KACpB;SAAM;QACH,OAAO,IAAI,CAAC;KACf;AACL,CAAC;AAjBD,wCAiBC;AAEM,KAAK,UAAU,oBAAoB,CAAC,IAAY;IACnD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAC3B,IAAI,GAAG,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,CAAC;IAErC,IAAG,MAAM,KAAK,OAAO,EAAE;QACnB,GAAG,GAAG,GAAG;aACb,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;aACnB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aACpB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;aACnB,iBAAiB,EAAE,CAAA;QACf,OAAO,GAAG,CAAC;KACd;IACD,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACvF,CAAC;AAbD,oDAaC"}