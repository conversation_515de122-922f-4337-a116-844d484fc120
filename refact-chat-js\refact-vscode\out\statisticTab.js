"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatisticTab = void 0;
/* eslint-disable @typescript-eslint/naming-convention */
// TODO: delete this file
const vscode = __importStar(require("vscode"));
const uuid_1 = require("uuid");
class StatisticTab {
    constructor(web_panel) {
        this.web_panel = web_panel;
        this._disposables = [];
        this.handleEvents = this.handleEvents.bind(this);
        this.web_panel.webview.onDidReceiveMessage(this.handleEvents);
    }
    handleEvents(message) {
        // switch (message.type) {
        //   case EVENT_NAMES_TO_STATISTIC.REQUEST_STATISTIC_DATA: {
        //     return fetchAPI
        //       .get_statistic_data()
        //       .then((data) => {
        //         return this.web_panel.webview.postMessage({
        //           type: EVENT_NAMES_TO_STATISTIC.RECEIVE_STATISTIC_DATA,
        //           payload: data,
        //         });
        //       })
        //       .catch((err) => {
        //         return this.web_panel.webview.postMessage({
        //           type: EVENT_NAMES_TO_STATISTIC.RECEIVE_STATISTIC_DATA_ERROR,
        //           payload: {
        //             message: err,
        //           },
        //         });
        //       });
        //   }
        // }
    }
    dispose() {
        this._disposables.forEach((d) => d.dispose());
    }
    get_html_for_statistic(webview, extensionUri, isTab = false) {
        const nonce = (0, uuid_1.v4)();
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(extensionUri, "node_modules", "refact-chat-js", "dist", "chat", "index.umd.cjs"));
        const styleMainUri = webview.asWebviewUri(vscode.Uri.joinPath(extensionUri, "node_modules", "refact-chat-js", "dist", "chat", "style.css"));
        const styleOverride = webview.asWebviewUri(vscode.Uri.joinPath(extensionUri, "assets", "custom-theme.css"));
        return `<!DOCTYPE html>
            <html lang="en" class="light">
            <head>
                <meta charset="UTF-8">
                <!--
                    Use a content security policy to only allow loading images from https or from our extension directory,
                    and only allow scripts that have a specific nonce.
                    TODO: remove  unsafe-inline if posable
                -->
                <meta http-equiv="Content-Security-Policy" script-src 'nonce-${nonce}'; style-src-attr 'sha256-tQhKwS01F0Bsw/EwspVgMAqfidY8gpn/+DKLIxQ65hg=' 'unsafe-hashes';">
                <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">

                <title>Refact.ai Chat</title>
                <link href="${styleMainUri}" rel="stylesheet">
                <link href="${styleOverride}" rel="stylesheet">
            </head>
            <body>
                <div id="refact-statistic"></div>
                <script nonce="${nonce}" src="${scriptUri}"></script>

                <script nonce="${nonce}">
                window.onload = function() {
                    const root = document.getElementById("refact-statistic")
                    RefactChat.renderStatistic(root, {host: "vscode", tabbed: ${isTab}})
                }
                </script>
            </body>
            </html>`;
    }
}
exports.StatisticTab = StatisticTab;
//# sourceMappingURL=statisticTab.js.map