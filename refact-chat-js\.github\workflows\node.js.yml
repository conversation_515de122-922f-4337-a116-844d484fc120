# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Node.js CI

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [lts/*, latest]

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"
      - run: npm ci
      - run: npm run test
      - run: npm run format:check
      - run: npm run types
      - run: npm run lint
      - run: npm run build

      - run: |
          name=$(echo -n "${{ matrix.node-version }}" | tr -cd '[[:alnum:]]')
          echo "ARTIFACT_NAME=$name" >> $GITHUB_ENV

      - name: Upload artifacts
        uses: actions/upload-artifact@v4

        with:
          name: refact-chat-js-${{ env.ARTIFACT_NAME }}
          if-no-files-found: ignore
          path: ./dist
