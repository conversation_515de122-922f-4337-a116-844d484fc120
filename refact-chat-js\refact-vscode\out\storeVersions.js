"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.filename_from_document = void 0;
/* eslint-disable @typescript-eslint/naming-convention */
const vscode = __importStar(require("vscode"));
function filename_from_document(document) {
    let file_name = document.uri.toString();
    let project_dir = vscode.workspace.getWorkspaceFolder(document.uri)?.uri.toString();
    if (project_dir !== undefined && file_name.startsWith(project_dir)) {
        // This prevents unnecessary user name and directory details from leaking
        let relative_file_name = file_name.substring(project_dir.length);
        if (relative_file_name.startsWith("/")) {
            relative_file_name = relative_file_name.substring(1);
        }
        return relative_file_name;
    }
    // As a fallback, return the full file name without any directory
    let last_slash = file_name.lastIndexOf("/");
    if (last_slash >= 0) {
        return file_name.substring(last_slash + 1);
    }
    return file_name;
}
exports.filename_from_document = filename_from_document;
//# sourceMappingURL=storeVersions.js.map