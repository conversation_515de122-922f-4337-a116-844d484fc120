<p align="center">
  <img alt="Refact" src="https://github.com/user-attachments/assets/190a9e7b-fd0b-4546-9213-1f5571346c8b"/>
</p>

---

[![Discord](https://img.shields.io/discord/1037660742440194089?logo=discord&label=Discord&link=https%3A%2F%2Fsmallcloud.ai%2Fdiscord)](https://smallcloud.ai/discord)
[![Twitter Follow](https://img.shields.io/twitter/follow/refact_ai)](https://twitter.com/intent/follow?screen_name=refact_ai)
![License](https://img.shields.io/github/license/smallcloudai/refact-vscode)

## Refact.ai - Your Customizable Open-Source AI Software Engineering Agent

Refact.ai is a free, **open-source** AI Agent that handles engineering tasks end-to-end. It deeply understands your codebases and integrates with your tools, databases, and browsers to automate complex, multi-step tasks.

- Integrate AI Agent with the tools you already use, allowing it to complete tasks for you end-to-end.
- Deploy Refact.ai on-premise and maintain **100% control over your codebase**.
- Access State-of-the-Art Models (Claude 3.7 Sonnet, GPT-4o, o3-mini, etc.)
- Bring your own key (BYOK) - Use your own API keys for external LLMs.
- Stop switching between your IDE and chat—Refact.ai has an integrated chat right in your IDE.
- Get free, unlimited, context-aware auto-completion.

Suitable for both individual and enterprise use, supporting 25+ programming languages, including Python, JavaScript, Java, Rust, TypeScript, PHP, C++, C#, Go, and more.

We can't say it better than our users: *'Refact.ai understands me better than my human coworkers. It's like working in perfect sync with an ideal partner.'*

## How does Refact.ai Agent empower developers?

- **Unlimited accurate auto-completion with context awareness for free** - Powered by Qwen2.5-Coder-1.5B, Refact.ai uses Retrieval-Augmented Generation (RAG) to provide precise, context-aware code suggestions. By retrieving relevant snippets and project-specific insights, it ensures completions that align with your coding style and architecture.![auto-completion](https://github.com/user-attachments/assets/0ffab638-c807-4863-8d62-a663b1459805)
- **Integrated in-IDE Chat** - By deeply understanding your codebases, Refact.ai provides relevant, intelligent answers to your questions—right inside your development environment.![refactor](https://github.com/user-attachments/assets/e7b2e779-85c5-46a0-99ad-ea909a69ddc7)
- **Integrated with the tools** - Refact.ai goes beyond codebases, connecting with GitHub, GitLab, databases (PostgreSQL, MySQL), Pdb, Docker, and any shell command, so AI Agent can autonomously complete tasks—from code generation to deployment.![integrations](https://github.com/user-attachments/assets/daf8d0ee-0a54-4aa8-82e5-f968fded0c7a)
- **State-of-the-Art Models** - Refact.ai supports **Claude 3.7 Sonnet, GPT-4o, and o3-mini**, allowing you to choose the best model for each task based on its complexity.
- **Bring your own key (BYOK)** - Use your own API keys for external LLMs, ensuring flexibility and control over your AI infrastructure.![BYOK](https://github.com/user-attachments/assets/44e416f7-fb4d-4846-a1e0-1b7da32c2c75)
- **Upload images**: Click the image button to add visual context to your chat.
- **Use @-commands** to control what context to use to answer your question:
    - **@web** - define a webpage.
    - **@file** - upload and attach a single file.
    - **@definition** - find and attach a definition.
    - **@references** - locate all references and attach them.
    - **@tree** - display the workspace directory and files tree.
    - Create your own **custom system prompt** for a more personalized workflow.![@-commands](https://github.com/user-attachments/assets/28e1db76-3490-4195-a3e0-de30496239a9)

## Refact.ai Agent For Enterprise
Deploying Refact.ai Agent is like adding a 24/7 engineer to your team—one that instantly understands your codebase, adapts to your workflows, accelerates development from day one, and becomes a shared knowledge base for your entire team.

1. **Refact.ai already understands your company's context:** AI Agent captures the unique structure, tools, and workflows of your organization, using your company's databases, documentation, and code architecture to deliver customized solutions.
2. **Gets smarter over time:** With each interaction and feedback, Refact.ai Agent adapts to your organization's needs, becoming more accurate and powerful.
3. **Organizes experience into the knowledge base:** Refact.ai Agent captures and shares knowledge from interactions with each team member. It not only streamlines workflows but also supports faster onboarding and smoother cross-functional collaboration across projects.

### Take full control of your AI Agent, tailored to your company:
- **Deploy Refact.ai on-premise:** on your own servers or private cloud. Your data never leaves your control. Telemetry from the plugins goes to your server and nowhere else. You can verify what the code does, it's open source.
- **Fine-tune a model on your codebase:** A fine-tuned code completion model will provide you with more relevant suggestions: it can memorize your coding style, the right way to use your internal APIs, and the tech stack you use.
- **Priority Support:** Our engineers are always available to assist you at every stage, from setup to fine-tuning and beyond.

**To get a 2-week free trial** for your team, just fill out the form on [our website](https://refact.ai/contact/?utm_source=vscode&utm_medium=marketplace&utm_campaign=enterprise). We'll reach out with more details!
\
&nbsp;
\
&nbsp;
## Which tasks can Refact.ai help me with?

- **Generate code from natural language prompts (even if you make typos)** - Instantly turn ideas into functional code, accelerating development and eliminating the blank-screen problem.
\
&nbsp;
  ![gen](https://github.com/user-attachments/assets/ef4acec7-4967-400a-900e-9d3382d05b1b) 
\
&nbsp;
- **Refactor code** - Improve code quality, readability, and efficiency with automated refactoring that aligns with best practices.
\
&nbsp;
  ![refactor](https://github.com/user-attachments/assets/2cae4467-f363-4033-8ecf-2854dcc74aaa) 
\
&nbsp;
- **Explain code** - Quickly understand complex or unfamiliar code with AI-generated explanations, making collaboration and onboarding faster.
\
&nbsp;
![explain](https://github.com/user-attachments/assets/bd43d9aa-15c9-49dc-9fa9-b5ab5c4ecdfe)
\
&nbsp;
- **Debug code** - Detect and fix errors faster with intelligent debugging, reducing time spent troubleshooting issues. 
\
&nbsp;
![ddbg](https://github.com/user-attachments/assets/45e917b5-f47b-4b84-b1f4-f4918c8a00c7) 
\
&nbsp;
- **Generate unit tests** -  Ensure code reliability by automatically creating comprehensive unit tests.
\
&nbsp;
  ![unit](https://github.com/user-attachments/assets/5168ee57-e35b-4484-bf19-70cc0f3a6299)
\
&nbsp;

- **Code Review** - Get AI-assisted code reviews for cleaner, more secure, and more efficient code, addressing developers' top concern: accuracy.
- **Create Documentation** - Automate documentation generation to keep knowledge accessible and up to date.
- **Generate Docstrings** - Enhance maintainability with clear, structured documentation generated for functions and classes in seconds.

## Join Our Discord Community

Connect with other developers in our [Discord community](https://www.smallcloud.ai/discord). Ask questions, share your opinion, propose new features.
