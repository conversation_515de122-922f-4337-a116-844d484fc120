import React from "react";
import classnames from "classnames";
import styles from "./coin.module.css";

export const Coin: React.FC<
  React.SVGProps<SVGSVGElement> & { className?: string }
> = ({ className, ...props }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="-20 -16 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      className={classnames(styles.coin, className)}
    >
      <circle
        cx="12"
        cy="16"
        r="30"
        strokeWidth="4"
        stroke="currentColor"
      ></circle>
      <path
        d="M0 31.9752V0H9.74697V4.01742H5.08967V27.9451H9.74697V31.9752H0Z"
        fill="currentColor"
      />
      <path
        d="M11.6278 16.095V13.5556C13.0526 13.5556 14.0409 13.2651 14.5927 12.6839C15.1444 12.1028 15.4203 11.1637 15.4203 9.86669V7.07471C15.4203 5.51659 15.6304 4.27009 16.0504 3.33522C16.4786 2.39192 17.0716 1.68024 17.8293 1.20017C18.5952 0.720104 19.5011 0.400057 20.5471 0.240034C21.593 0.0800112 22.7378 0 23.9814 0V4.01742C23.0096 4.01742 22.2683 4.14797 21.7577 4.40906C21.2553 4.66173 20.9136 5.05757 20.7324 5.5966C20.5512 6.1272 20.4606 6.80098 20.4606 7.61794V11.3069C20.4606 11.947 20.3453 12.5576 20.1147 13.1387C19.8841 13.7115 19.4558 14.221 18.8299 14.6674C18.204 15.1053 17.3104 15.4549 16.1492 15.716C14.988 15.9686 13.4808 16.095 11.6278 16.095ZM23.9814 31.9752C22.7378 31.9752 21.593 31.8951 20.5471 31.7351C19.5011 31.5751 18.5952 31.255 17.8293 30.775C17.0716 30.2949 16.4786 29.5832 16.0504 28.6399C15.6304 27.7051 15.4203 26.4586 15.4203 24.9004V22.0958C15.4203 20.7988 15.1444 19.8597 14.5927 19.2786C14.0409 18.6974 13.0526 18.4069 11.6278 18.4069V15.8676C13.4808 15.8676 14.988 15.9981 16.1492 16.2592C17.3104 16.5119 18.204 16.8614 18.8299 17.3078C19.4558 17.7457 19.8841 18.2553 20.1147 18.8364C20.3453 19.4091 20.4606 20.0197 20.4606 20.6683V24.3572C20.4606 25.1657 20.5512 25.8353 20.7324 26.3659C20.9136 26.9049 21.2553 27.3008 21.7577 27.5535C22.2683 27.8146 23.0096 27.9451 23.9814 27.9451V31.9752ZM11.6278 18.4069V13.5556H16.0627V18.4069H11.6278Z"
        fill="currentColor"
      />
    </svg>
  );
};
