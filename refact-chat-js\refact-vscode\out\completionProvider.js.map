{"version": 3, "file": "completionProvider.js", "sourceRoot": "", "sources": ["../src/completionProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yDAAyD;AACzD,+CAAiC;AACjC,iDAAmC;AACnC,qDAAuC;AACvC,kDAAoC;AACpC,uDAA+D;AAG/D,MAAa,0BAA0B;IAAvC;QAsFY,0BAAqB,GAAW,CAAC,CAAC;IAoF9C,CAAC;IAxKG,KAAK,CAAC,4BAA4B,CAC9B,QAA6B,EAC7B,QAAyB,EACzB,OAAuC,EACvC,WAAqC;QAGrC,IAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE;YAClC,OAAO,EAAE,CAAC;SACb;QAED,IAAI,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,KAAK,EAAE;YACP,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;gBACvF,OAAO,EAAE,CAAC;aACb;SACJ;QACD,IAAI,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAC3F,IAAI,gBAAgB,IAAI,OAAO,CAAC,WAAW,KAAK,MAAM,CAAC,2BAA2B,CAAC,SAAS,EAAE;YAC1F,OAAO,EAAE,CAAC;SACb;QAED,IAAI,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAE,qDAAqD;QACzF,IAAI,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,cAAc,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QACxE,IAAI,eAAe,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,sCAAsC,GAAG,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;QACzG,IAAI,CAAC,sCAAsC,EAAE;YACzC,OAAO,EAAE,CAAC;SACb;QACD,IAAI,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;QAC/D,IAAI,SAAS,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QACnC,IAAI,SAAS,CAAC,MAAM,GAAG,GAAG,GAAC,IAAI,EAAE,EAAE,4GAA4G;YAC3I,OAAO,EAAE,CAAC;SACb;QAED,qGAAqG;QACrG,IAAI,eAAe,GAAG,OAAO,CAAC,WAAW,KAAK,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC;QAExF,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,0BAA0B,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,EAAE;YACZ,8GAA8G;YAC9G,6BAA6B;YAC7B,IAAI,2BAA2B,GAAG,cAAc,CAAC,OAAO,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC;YACjG,0BAA0B,GAAG,QAAQ,CAAC,SAAS,CAAC;YAChD,0BAA0B,IAAI,cAAc,CAAC,MAAM,GAAG,2BAA2B,CAAC,MAAM,CAAC;SAC5F;QAED,IAAI,6BAA6B,GAAG,IAAI,CAAC;QACzC,CAAC,UAAU,EAAE,6BAA6B,CAAC,GAAG,MAAM,IAAI,CAAC,cAAc,CACnE,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,CAAC,IAAI,EACb,0BAA0B,EAC1B,SAAS,EACT,eAAe,CAClB,CAAC;QAEF,IAAI,OAAO,GAAG;YACV,OAAO,EAAE,4BAA4B;YACrC,KAAK,EAAE,gBAAgB;YACvB,SAAS,EAAE,CAAC,6BAA6B,CAAC;SAC7C,CAAC;QAEF,IAAI,cAAc,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC5E,IAAI,cAAc,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClF,IAAI,SAAS,EAAE;YACX,cAAc,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;SAC1D;QACD,OAAO,CAAC,GAAG,CAAC;YACR,YAAY,EAAE,UAAU;YACxB,gBAAgB,EAAE,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,SAAS;YAC/D,gBAAgB,EAAE,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,SAAS;SAClE,CAAC,CAAC;QACH,IAAI,cAAc,GAAG,IAAI,MAAM,CAAC,oBAAoB,CAChD,UAAU,EACV,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,cAAc,CAAC,EAChD,OAAO,CACV,CAAC;QACF,OAAO,CAAC,cAAc,CAAC,CAAC;IAC5B,CAAC;IAID,KAAK,CAAC,cAAc,CAChB,WAAqC,EACrC,SAAiB,EACjB,SAAiB,EACjB,WAAmB,EACnB,gBAAwB,EACxB,SAAkB,EAClB,eAAwB;QAGxB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACnB,MAAM,MAAM,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC;SAC9C;QACD,IAAI,WAAW,CAAC,uBAAuB,EAAE;YACrC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;SACnB;QAED,IAAI,OAAO,GAAG,IAAI,QAAQ,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAClE,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAC1F,IAAI,UAAkB,CAAC;QACvB,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACjD,UAAU,GAAG,EAAE,CAAC;SACnB;aAAM;YACH,UAAU,GAAG,WAAW,CAAC;SAC5B;QAED,IAAI,OAAO,GAA8B,EAAE,CAAC;QAC5C,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;QAE/B,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACpB,IAAI,OAAY,CAAC;QACjB,IAAI,QAAQ,GAAG,eAAe,CAAC;QAE/B,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;aAAM;YACH,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;SAClC;QAED,IAAI,WAAW,GAAG,GAAG,CAAC;QACtB,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,EAAE;YAChC,WAAW,GAAG,GAAG,CAAC;SACrB;QAED,OAAO,GAAG,QAAQ,CAAC,qBAAqB,CACpC,WAAW,EACX,OAAO,EACP,SAAS,EACT,SAAS,EACT,WAAW,EACX,gBAAgB,EAChB,UAAU,EACV,QAAQ,EACR,WAAW,CACd,CAAC;QACF,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;QACjD,IAAI,IAAS,CAAC;QACd,IAAI,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;QAChC,IAAI,IAAI,KAAK,SAAS,EAAE;YACpB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;SACnB;QACD,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACpB,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,CAAC,eAAe,MAAM,IAAI,CAAC,CAAC,CAAC;QAEzC,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;QACvD,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,EAAE,IAAI,UAAU,KAAK,IAAI,EAAE;YACtE,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,UAAU,CAAC,CAAC,CAAC;YACjD,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;SACnB;QACD,IAAI,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC5B,MAAM,CAAC,UAAU,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;QAC1D,OAAO,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;IACvC,CAAC;IAED,gBAAgB,CAAC,IAAyB;QACtC,IAAG,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO;SAAE;QAC7B,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,YAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACrE,CAAC;CACJ;AA1KD,gEA0KC;AAED,SAAgB,kBAAkB,CAAC,IAAsB;IAErD,IAAI,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/C,IAAI,GAAG,GAAG,MAAM,CAAC;IACjB,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;QACzB,IAAI,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrD,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE;YACtB,GAAG,GAAG,QAAQ,CAAC;SAClB;KACJ;IACD,OAAO,GAAG,CAAC;AACf,CAAC;AAXD,gDAWC;AAGM,KAAK,UAAU,eAAe,CAAC,aAAqB;IAEvD,IAAI,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;IACpD,IAAI,CAAC,GAAG,EAAE;QACN,OAAO,CAAC,GAAG,CAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC;KAC/D;IACD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;QACxB,sBAAsB,EAAE,aAAa;KACxC,CAAC,CAAC;IACH,MAAM,OAAO,GAAG;QACZ,cAAc,EAAE,kBAAkB;QAClC,uCAAuC;KAC1C,CAAC;IACF,IAAI,GAAG,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE;QAC/B,MAAM,EAAE,MAAM;QACd,OAAO,EAAE,OAAO;QAChB,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,QAAQ;QAClB,KAAK,EAAE,UAAU;QACjB,QAAQ,EAAE,aAAa;KAC1B,CAAC,CAAC;IAEH,IAAI;QACA,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KAC5B;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;KACzD;AACL,CAAC;AA3BD,0CA2BC;AAGD,SAAgB,eAAe,CAAC,MAAc;IAE1C,4CAA4C;AAChD,CAAC;AAHD,0CAGC;AAGD,SAAgB,eAAe;IAE3B,UAAU,CAAC,GAAG,EAAE;QACZ,eAAe,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC,CAAC;AACX,CAAC;AALD,0CAKC;AAGD,SAAgB,cAAc;IAE1B,UAAU,CAAC,GAAG,EAAE;QACZ,eAAe,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC,CAAC;AACX,CAAC;AALD,wCAKC;AAGD,SAAgB,cAAc;IAE1B,eAAe,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AAHD,wCAGC"}