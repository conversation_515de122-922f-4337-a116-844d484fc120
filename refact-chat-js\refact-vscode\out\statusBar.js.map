{"version": 3, "file": "statusBar.js", "sourceRoot": "", "sources": ["../src/statusBar.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yDAAyD;AACzD,+CAAiC;AACjC,uDAAyC;AACzC,kDAAoC;AAIpC,IAAI,gBAAgB,GAAG,EAAE,CAAC;AAC1B,IAAI,kBAAkB,GAAG,EAAE,CAAC;AAG5B,SAAgB,mBAAmB,CAAC,GAAW;IAE3C,gBAAgB,GAAG,GAAG,CAAC;AAC3B,CAAC;AAHD,kDAGC;AAGD,SAAgB,qBAAqB,CAAC,GAAW;IAE7C,kBAAkB,GAAG,GAAG,CAAC;AAC7B,CAAC;AAHD,sDAGC;AAED,MAAa,aAAa;IAA1B;QACI,SAAI,GAAQ,EAAE,CAAC;QACf,gBAAW,GAAY,KAAK,CAAC;QAC7B,oBAAe,GAAW,EAAE,CAAC;QAC7B,YAAO,GAAY,KAAK,CAAC;QACzB,kBAAa,GAAY,KAAK,CAAC;QAC/B,oBAAe,GAAY,KAAK,CAAC;QACjC,kBAAa,GAAW,EAAE,CAAC;QAC3B,aAAQ,GAAW,EAAE,CAAC;QACtB,oBAAe,GAAW,EAAE,CAAC;QAC7B,4BAAuB,GAAY,KAAK,CAAC;QACzC,iBAAY,GAAW,CAAC,CAAC,CAAC;QAC1B,eAAU,GAAW,EAAE,CAAC;QACxB,eAAU,GAAW,EAAE,CAAC;IA8L5B,CAAC;IA5LG,oBAAoB,CAAC,OAAgC;QAEjD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACrF,IAAI,CAAC,OAAO,GAAG,4BAA4B,CAAC;QAE5C,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;QAC1B,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,YAAY;QAER,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,kCAAkC,CAAC;YACpD,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,4DAA4D,CAAC;SACpF;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,+BAA+B,CAAC;YACjD,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC;YACrF,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;gBACjD,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,6EAA6E,IAAI,CAAC,eAAe,EAAE,CAAC;aAC3H;iBAAM;gBACH,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,4BAA4B,GAAG,IAAI,CAAC,eAAe,CAAC;aAC3E;SACJ;aAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;YACnF,IAAI,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACxF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,mEAAmE,KAAK,GAAG,CAAC;SACnG;aAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,wBAAwB,CAAC;YAC1C,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;SACzC;aAAM,IAAI,IAAI,CAAC,aAAa,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,qCAAqC,CAAC;YACvD,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC;YACrF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,mCAAmC,CAAC;SAC3D;aAAM,IAAI,IAAI,CAAC,eAAe,EAAE;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,uCAAuC,CAAC;YACzD,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC;YACrF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,mCAAmC,CAAC;SAC3D;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,EAAE,EAAE;YAClC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,+BAA+B,CAAC;YACjD,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC;YACrF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;SAC1C;aAAM,IAAI,IAAI,CAAC,uBAAuB,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,IAAI,0BAA0B,CAAC;YAC/D,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;YACtC,IAAI,GAAG,GAAW,EAAE,CAAC;YACrB,IAAI,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACxF,IAAI,KAAK,EAAE;gBACP,GAAG,IAAI,6BAA6B,KAAK,EAAE,CAAC;aAC/C;YACD,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,GAAG,EAAE;oBACL,GAAG,IAAI,MAAM,CAAC;iBACjB;gBACD,GAAG,IAAI,yBAAyB,IAAI,CAAC,eAAe,EAAE,CAAC;aAC1D;YACD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,GAAG,EAAE;oBACL,GAAG,IAAI,MAAM,CAAC;iBACjB;gBACD,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;aAC/B;YACD,IAAI,gBAAgB,IAAI,kBAAkB,EAAE;gBACxC,GAAG,IAAI,MAAM,CAAC;gBACd,GAAG,IAAI,gBAAgB,IAAI,kBAAkB,CAAC;aACjD;YACD,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;SAC3B;aAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACxC,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;YACnF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,gBAAgB,IAAI,gBAAgB,CAAC;SAC5D;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,IAAI,0BAA0B,CAAC;YAC/D,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;YACtC,IAAI,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACxF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,gBAAgB,IAAI,kBAAkB,IAAI,6CAA6C,KAAK,GAAG,CAAC;YACpH,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;aACjD;SACJ;IACL,CAAC;IAED,iBAAiB,CAAC,EAAW;QAEzB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,gBAAgB,CAAC,KAAc,EAAE,MAAwB;QAErD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC5B,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE;gBACrB,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;aAC7C;YACD,IAAI,MAAM,KAAK,IAAI,EAAE;gBACjB,IAAI,CAAC,eAAe,GAAG,GAAG,MAAM,EAAE,CAAC;aACtC;iBAAM;gBACH,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;aAC7B;SACJ;aAAM;YACH,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;SAC7B;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;SAC7B;QACD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;SACxC;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,gBAAgB,CAAC,KAAa;QAE1B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,uBAAuB,CAAC,UAAkB;QAEtC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,wBAAwB;QACpB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,0BAA0B;QACtB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,WAAW,CAAC,KAAa;QACrB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,iBAAiB,CAAC,MAAiB;QAE/B,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAC5E,MAAM,gBAAgB,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC;YACnF,IAAI,CAAC,UAAU,GAAG,sBAAsB,gBAAgB,IAAI,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;SAC1F;QACD,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC5D,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,SAAS,EAAE;gBAChC,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC;gBAC1E,IAAI,CAAC,UAAU,GAAG,wBAAwB,cAAc,IAAI,MAAM,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC;aACzF;iBAAM,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,UAAU,EAAE;gBACxC,IAAI,CAAC,UAAU,GAAG,2BAA2B,CAAC;aACjD;iBAAM,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,UAAU,EAAE;gBACxC,IAAI,CAAC,UAAU,GAAG,uBAAuB,CAAC;aAC7C;SACJ;QAED,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,MAAM,CAAC,GAAG,EAAE;YACZ,UAAU;gBACN,cAAc,MAAM,CAAC,GAAG,CAAC,qBAAqB,IAAI;oBAClD,gBAAgB,MAAM,CAAC,GAAG,CAAC,uBAAuB,MAAM,CAC3D;SACJ;aAAM;YACH,UAAU,IAAI,oBAAoB,CAAC;SACtC;QACD,IAAI,MAAM,CAAC,KAAK,EAAE;YACd,UAAU;gBACN,eAAe,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI;oBACvC,gBAAgB,MAAM,CAAC,KAAK,CAAC,aAAa,IAAI;oBAC9C,iCAAiC,MAAM,CAAC,KAAK,CAAC,yBAAyB,IAAI;oBAC3E,yCAAyC,MAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,CACvF;SACJ;aAAM;YACH,UAAU,IAAI,sBAAsB,CAAC;SACxC;QACD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;QAEpC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;CACJ;AA3MD,sCA2MC;AAGD,KAAK,UAAU,uBAAuB,CAAC,MAAqC;IAExE,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;AACrC,CAAC;AAGD,SAAgB,eAAe;IAE3B,IAAI,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,uBAAuB,CAAC,CAAC;IACrF,IAAI,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;IACpD,IAAI,cAAc,EAAE;QAChB,uBAAuB,CAAC,cAAc,CAAC,CAAC;KAC3C;IACD,OAAO,CAAC,WAAW,CAAC,CAAC;AACzB,CAAC;AARD,0CAQC;AAEM,KAAK,UAAU,mCAAmC,CACrD,QAAiB,EACjB,KAAa,EACb,WAAmB,EACnB,aAA2B,EAC3B,UAA8B;IAE9B,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,YAAY,GAAG,KAAK,CAAC;IACzB,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACnC,IAAI,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YACtE,eAAe,GAAG,IAAI,CAAC;SAC1B;QACD,IAAI,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YAChE,QAAQ,GAAG,IAAI,CAAC;SACnB;QACD,IAAI,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YACnE,YAAY,GAAG,IAAI,CAAC;SACvB;QACD,IAAI,aAAa,YAAY,KAAK,IAAI,aAAa,CAAC,OAAO,EAAE;YACzD,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC;SACzC;aAAM;YACH,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;SACjD;KACJ;IACD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACnC,IAAI,aAAa,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YAC3C,eAAe,GAAG,IAAI,CAAC;SAC1B;QACD,IAAI,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YAC5E,QAAQ,GAAG,IAAI,CAAC;SACnB;QACD,IAAI,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YACxC,YAAY,GAAG,IAAI,CAAC;SACvB;KACJ;IACD,IAAI,CAAC,QAAQ,EAAE;QACX,MAAM,CAAC,UAAU,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;QAC3C,MAAM,OAAO,CAAC,aAAa,EAAE,CAAC;KACjC;SAAM;QACH,MAAM,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;KAC5C;IACD,IAAI,aAAa,CAAC,MAAM,GAAG,GAAG,EAAE;QAC5B,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;KACzD;IACD,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;AACjE,CAAC;AA/CD,kFA+CC;AAED,kBAAe,aAAa,CAAC"}